'use client';

import { useRef, useEffect, useState, useMemo, useCallback, memo } from 'react';
import { OrbitControls } from '@react-three/drei';
import * as THREE from 'three';
import { loadImageWithCache } from '@/lib/asset-loader';
import { degToRad } from 'three/src/math/MathUtils';

// Memoized OrbitControls component to prevent unnecessary re-renders
const MemoizedOrbitControls = memo(OrbitControls);

function PanoramicSphere({
  currentImage,// This prop holds the camera position and rotation data
  imageUrl,
  imageId,
  textureCache,
  setTextureCache,
  loadingQueue,
  setLoadingQueue,
  onTextureLoad,
}) {
  const meshRef = useRef();
  const controlsRef = useRef();
  const [currentTexture, setCurrentTexture] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  // Memoized geometry and material for better performance
  const sphereGeometry = useMemo(() => {
    const geometry = new THREE.SphereGeometry(32, 60, 40);
    // Dispose of geometry on unmount
    return geometry;
  }, []);

  const basicMaterial = useMemo(() => {
    const material = new THREE.MeshBasicMaterial({
      side: THREE.BackSide,
      transparent: false,
    });
    return material;
  }, []);

  // Memoized OrbitControls configuration
  const controlsConfig = useMemo(() => ({
    enableZoom: false,
    enablePan: false,
    enableRotate: true,
    enableDamping: true,
    dampingFactor: 0.05,
    rotateSpeed: -0.35,
    maxDistance:0.5,
    minDistance:0,
    minPolarAngle: degToRad(60),
    maxPolarAngle: degToRad(120),
    minAzimuthAngle: -Infinity,
    maxAzimuthAngle: Infinity,
  }), []);

  // Cleanup geometries and materials on unmount
  useEffect(() => {
    return () => {
      sphereGeometry?.dispose();
      basicMaterial?.dispose();
      currentTexture?.dispose();
    };
  }, [sphereGeometry, basicMaterial, currentTexture]);

  // Optimized texture configuration function
  const configureTexture = useCallback((texture) => {
    texture.mapping = THREE.EquirectangularReflectionMapping;
    texture.wrapS = THREE.RepeatWrapping;
    texture.wrapT = THREE.ClampToEdgeWrapping;
    texture.minFilter = THREE.LinearFilter;
    texture.magFilter = THREE.LinearFilter;
    texture.flipY = true;
    // Set proper color space for accurate color representation
    texture.colorSpace = THREE.SRGBColorSpace;
    texture.needsUpdate = true;
    return texture;
  }, []);

  // Enhanced texture loading with proper Three.js TextureLoader and caching
  const loadTexture = useCallback(async (url, id) => {
    if (textureCache.has(id)) {
      return textureCache.get(id);
    }

    setIsLoading(true);

    try {
      // Use Three.js TextureLoader with proper cross-origin settings
      const textureLoader = new THREE.TextureLoader();

      // Set cross-origin to handle local assets properly
      textureLoader.setCrossOrigin('anonymous');

      const texture = await new Promise((resolve, reject) => {
        const timeoutId = setTimeout(() => {
          reject(new Error('Texture loading timeout'));
        }, 15000); // 15 second timeout for large panoramic images

        textureLoader.load(
          url,
          (loadedTexture) => {
            clearTimeout(timeoutId);
            configureTexture(loadedTexture);
            resolve(loadedTexture);
          },
          undefined, // onProgress
          (error) => {
            clearTimeout(timeoutId);
            reject(error);
          }
        );
      });

      // Cache the loaded texture
      setTextureCache(prevCache => {
        const newCache = new Map(prevCache);
        newCache.set(id, texture);
        return newCache;
      });

      return texture;
    } catch (error) {
      console.error(`PanoramicSphere - Error loading texture for ID ${id}:`, error);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [textureCache, setTextureCache, configureTexture, setIsLoading]);

  // Optimized background texture loading with better performance
  useEffect(() => {
    if (loadingQueue.length === 0) return;

    let isCancelled = false;

    const loadNextTexture = async () => {
      if (isCancelled) return;

      const nextItem = loadingQueue[0];
      if (!nextItem) return;

      // Skip if already cached
      if (textureCache.has(nextItem._id)) {
        setLoadingQueue(prev => prev.slice(1));
        return;
      }

      try {
        // Update status to downloading
        setLoadingQueue(prev =>
          prev.map(item =>
            item._id === nextItem._id
              ? { ...item, status: 'downloading' }
              : item
          )
        );

        // Load texture with cancellation check
        if (!isCancelled) {
          await loadTexture(nextItem.url, nextItem._id);
        }

        // Remove from queue if not cancelled
        if (!isCancelled) {
          setLoadingQueue(prev =>
            prev.filter(item => item._id !== nextItem._id)
          );
        }
      } catch (error) {
        console.error(`Background texture loading failed for ID ${nextItem._id}:`, error);
        if (!isCancelled) {
          setLoadingQueue(prev =>
            prev.filter(item => item._id !== nextItem._id)
          );
        }
      }
    };

    // Use requestIdleCallback for better performance if available
    const scheduleLoad = () => {
      if (typeof window !== 'undefined' && window.requestIdleCallback) {
        window.requestIdleCallback(loadNextTexture, { timeout: 1000 });
      } else {
        setTimeout(loadNextTexture, 100);
      }
    };

    scheduleLoad();

    return () => {
      isCancelled = true;
    };
  }, [loadingQueue, textureCache, setLoadingQueue, loadTexture]);

  // Optimized current texture loading with better state management
  useEffect(() => {
    if (!imageUrl || !imageId) {
      setCurrentTexture(null);
      return;
    }

    let isCancelled = false;

    const loadCurrentTexture = async () => {
      try {
        const texture = await loadTexture(imageUrl, imageId);
        if (texture && !isCancelled) {
          setCurrentTexture(texture);
          onTextureLoad?.();
        } else if (!isCancelled) {
          setCurrentTexture(null);
        }
      } catch (error) {
        console.error('Error loading current texture:', error);
        if (!isCancelled) {
          setCurrentTexture(null);
        }
      }
    };

    loadCurrentTexture();

    return () => {
      isCancelled = true;
    };
  }, [imageUrl, imageId, loadTexture, onTextureLoad]);

  // Update material when texture changes
  useEffect(() => {
    if (basicMaterial && currentTexture) {
      basicMaterial.map = currentTexture;
      basicMaterial.needsUpdate = true;
    } else if (basicMaterial) {
      basicMaterial.map = null;
      basicMaterial.needsUpdate = true;
    }
  }, [basicMaterial, currentTexture]);

  // Memoized mesh rotation
  const meshRotation = useMemo(() => [0, currentImage?._360Rotation || 0, 0], [currentImage?._360Rotation]);

  // Render nothing if texture is not yet loaded (show loading state)
  if (!currentTexture) {
    return (
      <MemoizedOrbitControls
        ref={controlsRef}
        {...controlsConfig}
        target-y={currentImage?.cameraPosition || 0}
      />
    );
  }

  return (
    <>
      {/* Optimized OrbitControls for camera interaction */}
      <MemoizedOrbitControls
        ref={controlsRef}
        {...controlsConfig}
      />

      {/* Optimized panoramic sphere mesh */}
      <mesh
        ref={meshRef}
        rotation={meshRotation}
        scale={[1, 1, -1]}
      >
        <primitive object={sphereGeometry} />
        <primitive object={basicMaterial} />
      </mesh>
    </>
  );
}

// Export memoized component for better performance
export default memo(PanoramicSphere);