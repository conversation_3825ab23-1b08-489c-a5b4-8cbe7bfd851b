'use client';

import { useState, useEffect } from 'react';
import { MdSave, MdCancel, MdCloudUpload, MdImage } from 'react-icons/md';

export default function InfoMarkerForm({ 
  infoMarker = null, 
  onSave, 
  onCancel, 
  isLoading = false 
}) {
  const [formData, setFormData] = useState({
    title: '',
    body1: '',
    body2: '',
    image: '',
  });
  const [errors, setErrors] = useState({});
  const [imageFile, setImageFile] = useState(null);
  const [imagePreview, setImagePreview] = useState('');
  const [uploading, setUploading] = useState(false);

  // Initialize form data when infoMarker prop changes
  useEffect(() => {
    if (infoMarker) {
      setFormData({
        title: infoMarker.title || '',
        body1: infoMarker.body1 || '',
        body2: infoMarker.body2 || '',
        image: infoMarker.image || '',
      });
      setImagePreview(infoMarker.image || '');
    } else {
      setFormData({
        title: '',
        body1: '',
        body2: '',
        image: '',
      });
      setImagePreview('');
    }
  }, [infoMarker]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const handleImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setImageFile(file);
      
      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreview(e.target.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const uploadImage = async () => {
    if (!imageFile) return null;
    
    setUploading(true);
    try {
      const formData = new FormData();
      formData.append('files', imageFile);
      
      const response = await fetch('/api/upload/info-markers', {
        method: 'POST',
        body: formData,
      });
      
      const result = await response.json();
      
      if (result.success && result.data.length > 0) {
        return result.data[0].url;
      } else {
        throw new Error('Upload failed');
      }
    } catch (error) {
      console.error('Image upload error:', error);
      throw error;
    } finally {
      setUploading(false);
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.title.trim()) {
      newErrors.title = 'Title is required';
    }
    
    if (!formData.body1.trim()) {
      newErrors.body1 = 'Body 1 is required';
    }
    
    if (!formData.body2.trim()) {
      newErrors.body2 = 'Body 2 is required';
    }
    
    if (!formData.image && !imageFile) {
      newErrors.image = 'Image is required';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    try {
      let imageUrl = formData.image;
      
      // Upload new image if selected
      if (imageFile) {
        imageUrl = await uploadImage();
      }
      
      const submitData = {
        ...formData,
        image: imageUrl,
      };
      
      await onSave(submitData);
    } catch (error) {
      console.error('Form submission error:', error);
      setErrors({ submit: 'Failed to save info marker. Please try again.' });
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h2 className="text-xl font-bold text-gray-900 mb-6">
        {infoMarker ? 'Edit Info Marker' : 'Create New Info Marker'}
      </h2>
      
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Title */}
        <div>
          <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
            Title *
          </label>
          <input
            type="text"
            id="title"
            name="title"
            value={formData.title}
            onChange={handleInputChange}
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.title ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="Enter info marker title"
          />
          {errors.title && (
            <p className="mt-1 text-sm text-red-600">{errors.title}</p>
          )}
        </div>

        {/* Body 1 */}
        <div>
          <label htmlFor="body1" className="block text-sm font-medium text-gray-700 mb-2">
            Body 1 *
          </label>
          <textarea
            id="body1"
            name="body1"
            value={formData.body1}
            onChange={handleInputChange}
            rows={4}
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.body1 ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="Enter first body content"
          />
          {errors.body1 && (
            <p className="mt-1 text-sm text-red-600">{errors.body1}</p>
          )}
        </div>

        {/* Body 2 */}
        <div>
          <label htmlFor="body2" className="block text-sm font-medium text-gray-700 mb-2">
            Body 2 *
          </label>
          <textarea
            id="body2"
            name="body2"
            value={formData.body2}
            onChange={handleInputChange}
            rows={4}
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.body2 ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="Enter second body content"
          />
          {errors.body2 && (
            <p className="mt-1 text-sm text-red-600">{errors.body2}</p>
          )}
        </div>

        {/* Image Upload */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Image *
          </label>
          
          {/* Image Preview */}
          {imagePreview && (
            <div className="mb-4">
              <img
                src={imagePreview}
                alt="Preview"
                className="w-32 h-32 object-cover rounded-md border border-gray-300"
              />
            </div>
          )}
          
          {/* File Input */}
          <div className="flex items-center space-x-4">
            <label className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 cursor-pointer">
              <MdCloudUpload className="mr-2" />
              Choose Image
              <input
                type="file"
                accept="image/*"
                onChange={handleImageChange}
                className="hidden"
              />
            </label>
            
            {imageFile && (
              <span className="text-sm text-gray-600">
                {imageFile.name}
              </span>
            )}
          </div>
          
          {errors.image && (
            <p className="mt-1 text-sm text-red-600">{errors.image}</p>
          )}
        </div>

        {/* Submit Error */}
        {errors.submit && (
          <div className="bg-red-50 border border-red-200 rounded-md p-3">
            <p className="text-red-600 text-sm">{errors.submit}</p>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 flex items-center"
          >
            <MdCancel className="mr-2" />
            Cancel
          </button>
          
          <button
            type="submit"
            disabled={isLoading || uploading}
            className={`px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center ${
              isLoading || uploading ? 'opacity-50 cursor-not-allowed' : ''
            }`}
          >
            <MdSave className="mr-2" />
            {isLoading || uploading ? 'Saving...' : 'Save Info Marker'}
          </button>
        </div>
      </form>
    </div>
  );
}
