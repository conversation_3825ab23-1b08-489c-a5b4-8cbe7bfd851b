'use client';
import React, { useRef, useState, useEffect, useCallback } from 'react';

import { FaUser } from "react-icons/fa"
import ImageScalerComponent from './ImageScalerComponent';
import {
    validateBookingForm,
    prepareBookingData,
    formatCurrency
} from '@/lib/package-client-utils';
import EnhancedBookingCalendar from './EnhancedBookingCalendar';
import { useRouter } from 'next/navigation';

const cssText1='text-xl font-bold uppercase  placeholder:text-white'
const cssText2='text-sm uppercase placeholder:text-white'

function InputComponent(props) {    
    const refInput=useRef(null)
    const {className1,icon,name,setInput,...others}=props   
     const [selected, setSelected] = useState(null);
     
     const handleChange = (e) => {
        setSelected(e.target.value);
        setInput(prev => ({ ...prev, [name]: e.target.value }));
    };
    // refInput.current?.style{{}}
    // console.log('InputComponent:',refInput.current?.style)
    return(
        <div className="flex items-center h-10 gap-2 mt-0 border-6 border-white rounded-xl">
            {icon && <div className='flex items-center justify-center w-fit h-full bg-white/60 p-1'>{icon}</div>}
            <input 
                ref={refInput} 
                // style={{}}
                checked={selected === others?.name}
                onChange={e=>handleChange(e)} 
                className={`flex items-center w-full mt select-none text-xs justify-center px-2 h-full placeholder:text-white placeholder:text-xs outline-none focus:ring-black ${selected === others?.name && 'text-black'} placeholder:${className1}`} {...others} 
            />
        </div>
    )
}

function InputRadioComponent(props) {
    const {className1,name,onCategoryChange,item}=props
    // console.log('InputRadioComponent:',className1)
    return(
        <div className="flex flex-col items-center h-fit gap-2 mt-0">
            <label className="flex flex-col  items-center justify-center">
                <ImageScalerComponent src={item.src} alt={'icon'}/>
            </label>
            <div className='flex w-fit h-fit border-6 border-white rounded-full'>
                <input
                    onChange={e => onCategoryChange(e.target.value)}
                    value={item.name}
                    id={item.name}
                    name={name}
                    type='radio'
                    className={`flex  w-5 h-5 border-6 m-1 text-black focus:text-white border-none ${className1} rounded-full`}
                />
            </div>
        </div>
    )
}

export default function BookingFormComponent() {
    const router = useRouter();
    const [input,setInput]=useState({})
    const [error,setError]=useState()
    const [success,setSuccess]=useState()
    const [packages, setPackages] = useState([])
    const [selectedPackage, setSelectedPackage] = useState(null)
    const [isLoading, setIsLoading] = useState(true)
    const [checkInDate, setCheckInDate] = useState(null)
    const [checkOutDate, setCheckOutDate] = useState(null)
    const [numberOfNights, setNumberOfNights] = useState(0)
    // Fetch packages on component mount
    useEffect(() => {
        fetchPackages();
    }, []);

    const fetchPackages = async () => {
        try {
            setIsLoading(true);
            setError(null);

            const response = await fetch('/api/packages');
            const data = await response.json();

            if (data.success) {
                // Filter to only show predefined package types
                const predefinedPackages = data.data.filter(pkg =>
                    ['individual', 'couples', 'families'].includes(pkg.category)
                );
                setPackages(predefinedPackages);
            } else {
                setError('Failed to load packages');
            }
        } catch (err) {
            console.error('Error fetching packages:', err);
            setError('Failed to load packages');
        } finally {
            setIsLoading(false);
        }
    };

    // Handle category selection and find corresponding package
    const handleCategoryChange = (category) => {
        setInput(prev => ({ ...prev, category }));

        // Find the package that matches this category
        const matchingPackage = packages.find(pkg => pkg.category === category);
        if (matchingPackage) {
            setSelectedPackage(matchingPackage);
            setInput(prev => ({
                ...prev,
                packageId: matchingPackage._id,
                category: category
            }));
        }
    };

    const handleDateRangeChange = useCallback((dateRange) => {
        setCheckInDate(dateRange.checkIn);
        setCheckOutDate(dateRange.checkOut);
        setNumberOfNights(dateRange.nights);

        // Update input state with dates
        setInput(prev => ({
            ...prev,
            checkIn: dateRange.checkIn,
            checkOut: dateRange.checkOut,
            nights: dateRange.nights
        }));
    }, [setCheckInDate, setCheckOutDate, setNumberOfNights, setInput]);

    const handleSubmit = async (e) => {
        e.preventDefault();
        // console.log('handleSubmit:', input);

        try {
            // Validate form data using client-safe validation
            const validation = validateBookingForm(input);

            if (!validation.isValid) {
                const firstError = Object.values(validation.errors)[0];
                setError(firstError);
                return;
            }

            // Check if package is selected
            if (!selectedPackage) {
                setError('Please select a package category');
                return;
            }

            // Check if dates are selected
            if (!checkInDate || !checkOutDate) {
                setError('Please select check-in and check-out dates');
                return;
            }

            // Check if at least one night is selected
            if (numberOfNights < 1) {
                setError('Please select at least one night');
                return;
            }

            // Prepare booking data using client-safe utility
            const bookingData = prepareBookingData(input, selectedPackage);

            setIsLoading(true);
            setError(null);

            const response = await fetch('/api/bookings', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(bookingData),
            });

            const data = await response.json();

            if (data.success) {
                setSuccess('Booking created successfully! Redirecting to payment...');

                // Clear form
                setInput({});
                setSelectedPackage(null);
                setCheckInDate(null);
                setCheckOutDate(null);
                setNumberOfNights(0);

                // Redirect to payment page after a shorter delay for better performance
                setTimeout(() => {
                    router.push(`/payment/${data.data._id}`);
                }, 1000);
            } else {
                setError(data.message || 'Failed to submit booking request');
            }
        } catch (err) {
            console.error('Booking submission error:', err);
            setError(err.message || 'Failed to submit booking request. Please try again.');
        } finally {
            setIsLoading(false);
        }
    };

    // console.log(input)
  return (
    <div className='booing-fom flex text-white w-full h-full items-start'>
        <div className='flex max-w-5xl h-full items-center'>
            <div className='flex flex-col w-1/2 h-fit px-2'>
                <div className='flex flex-col w-[80%] h-full gap-10 px-2'>
                    <span className='text-5xl uppercase leading-14 tracking-tight font-medium'>fill the form and send booking request</span>
                    <div className='flex w-86 flex-col leading-0'>
                        <span className='text-3xl tracking-tight font-light leading-8'>Upon submission and approvalof your request, an email link will be sent to you  to process payment</span>
                    </div>
                    <div className='flex w-60 flex-col leading-0'>
                        <span className='text-xl W-20 leading-3 tracking-tight uppercase font-light'>"Ensuring your stay at <span className='text-2xl uppercase font-bold tracking-tight'>elephant island</span>"</span>
                    </div>
                    <div className='flex w-60 flex-col leading-0'>
                        <span className='uppercase leading-5 text-xs'>should there be any queries please contact us at (+267)76123456 or email</span>
                        <span className='lowercase leading-5 font-bold text-xs'><EMAIL></span>
                    </div>
                </div>
            </div>
            <form className='flex flex-col gap-2 w-1/2 h-fit px-2 justify-center'>
                {/* Error Message */}
                {error && (
                    <div className=" text-white p-1 rounded-md text-sm">
                        {error}
                    </div>
                )}

                {/* Success Message */}
                {success && (
                    <div className="bg-green-500 text-white p-3 rounded-md text-sm">
                        {success}
                    </div>
                )}

                {/* Loading State */}
                {isLoading && (
                    <div className=" text-white p-1 rounded-md text-sm">
                        Loading packages...
                    </div>
                )}

                <div className='flex flex-col w-full'>
                    <span className={cssText1}>choose category</span>
                    <fieldset className='flex items-center w-full justify-between'>
                        {[
                            {name:'individual',src:'/assets/individuals_btn_ov.png'},
                            {name:'families',src:'/assets/families_btn_ov.png'},
                            {name:'couples',src:'/assets/couples_btn_ov.png'}
                        ].map((i,index)=>(
                            <InputRadioComponent
                                key={index}
                                item={i}
                                onCategoryChange={handleCategoryChange}
                                name='category'
                                value={i}
                                type='radio'
                            />
                        ))}
                    </fieldset>
                </div>
                <div className='flex w-full gap-4'>
                    <div className='flex flex-col w-full gap-1'>
                        <span className={cssText2}>firstname</span>
                        <InputComponent setInput={setInput} placeholder='John' name='firstname' className1={'uppercase'} type='text' />
                    </div>
                    <div className='flex flex-col w-full gap-1'>
                        <span className={cssText2}>surname</span>
                        <InputComponent setInput={setInput} placeholder='Doe' name='surname' className1={'uppercase'} type='text' />
                    </div>
                </div>
                <div className='flex w-full gap-4'>
                    <div className='flex flex-col w-full gap-1'>
                        <span className={cssText2}>email</span>
                        <InputComponent setInput={setInput} placeholder='<EMAIL>' name='email' className1={'lowercase'} type='email' />
                    </div>
                    <div className='flex flex-col w-full gap-1'>
                        <span className={cssText2}>phone</span>
                        <InputComponent setInput={setInput} placeholder='76123456' name='phone' type='tel' />
                    </div>
                </div>
                {/* Calendar for date selection */}
                <div className="w-full">
                    <EnhancedBookingCalendar
                        onDateRangeChange={handleDateRangeChange}
                        selectedPackage={selectedPackage}
                        isLoading={isLoading}
                        className="w-full"
                    />
                </div>
                <div className='flex flex-col w-1/2 gap-1'>
                    <div className='flex flex-col w-full gap-1'>
                        <span className={cssText2}>number of guests</span>
                        <InputComponent setInput={setInput} icon={<FaUser className="text-xl"/>} name='numberOfGuests' placeholder='5' type='number' />
                    </div>
                </div>

                {/* Selected Package Info */}
                {selectedPackage && (
                    <div className="bg-white/20 py-1 px-2 rounded-md text-white text-sm">
                        <h4 className="text-sm capitalize font-bold">{selectedPackage.name} Package</h4>
                        <div className="flex justify-between mt-0 text-sm">
                            <p className="text-xs">{selectedPackage.shortDescription}</p>
                            <p>Price: {formatCurrency(selectedPackage.pricing)}</p>
                            {checkInDate && checkOutDate && (
                                <p className="text-xs">
                                    {checkInDate.toLocaleDateString()} → {checkOutDate.toLocaleDateString()}
                                </p>
                            )}
                        </div>
                    </div>
                )}

                <button
                    onClick={handleSubmit}
                    disabled={isLoading || !selectedPackage || !checkInDate || !checkOutDate}
                    className={`flex items-center justify-center w-full h-12 gap-2 mt-1 border-6 border-white rounded-xl cursor-pointer ${
                        isLoading || !selectedPackage || !checkInDate || !checkOutDate ? 'cursor-not-allowed' : 'hover:bg-white/50'
                    }`}
                >
                    {isLoading ? 'Processing...' : isLoading || !selectedPackage || !checkInDate || !checkOutDate ? 'Please select a package and dates' : 'Book Now and Pay'}
                </button>
            </form>
        </div>
    </div>
  )
}
