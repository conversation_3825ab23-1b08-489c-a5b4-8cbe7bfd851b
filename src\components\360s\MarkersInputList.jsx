'use client';

import { settings } from '@/lib/settings';
import React, { useEffect, useState, useCallback, useMemo, memo, useRef } from 'react'
import { BiTrash } from 'react-icons/bi'

const css='flex items-center w-full h-8 bg-gray-50 rounded shadow placeholder:text-xs text-xs placeholder:text-gray-600 text-gray-600 px-2 outline-none'

function MarkersInputList({_360sList,_360Object, set_360Object}) {
  const [markerList, setMarkerList] = useState([])
  const [input, setInput] = useState({ name: '', markerType: '' })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitStatus, setSubmitStatus] = useState({ type: '', message: '' })
  const debounceTimeoutRef = useRef(null)

  // Enhanced handleAddList function with validation and feedback
  const handleAddList = useCallback(() => {
    // Validate input
    if (!input?.name || !input?.markerType) {
      setSubmitStatus({
        type: 'error',
        message: 'Please enter both marker name and type before adding.'
      })
      return
    }

    // Check for duplicate marker names
    const isDuplicate = markerList.some(marker =>
      marker.name.toLowerCase() === input.name.toLowerCase()
    )

    if (isDuplicate) {
      setSubmitStatus({
        type: 'error',
        message: 'A marker with this name already exists. Please use a different name.'
      })
      return
    }

    // Create new marker with default values
    const newMarker = {
      name: input.name.trim(),
      markerType: input.markerType,
      x: 0,
      y: 0,
      z: 0,
      // Add additional fields based on marker type
      ...(input.markerType === 'landingPage' ||
          input.markerType === 'guide' ||
          input.markerType === 'upstairs' ||
          input.markerType === 'downstairs'
          ? { _360Name: '' }
          : { infoType: '' })
    }

    // Add marker to list and immediately update parent state
    const updatedMarkerList = [...markerList, newMarker]
    setMarkerList(updatedMarkerList)

    // Immediately update parent state for real-time UI sync
    if (typeof set_360Object === 'function') {
      set_360Object(prev => ({
        ...prev,
        markerList: updatedMarkerList
      }))
    }

    // Clear input and show success message
    setInput({ name: '', markerType: '' })
    setSubmitStatus({
      type: 'success',
      message: `Marker "${newMarker.name}" added successfully! Don't forget to submit to save changes.`
    })

    // console.log('New marker added:', newMarker)
  }, [input, markerList, set_360Object])

  // Memoize the marker list from _360Object to prevent unnecessary updates
  const currentMarkerList = useMemo(() => {
    return _360Object?.markerList || []
  }, [_360Object?.markerList])

  // Memoize the current _360Object ID to prevent unnecessary re-renders
  const current360Id = useMemo(() => _360Object?._id, [_360Object?._id])

  // Initialize marker list from _360Object only once when component mounts or _360Object changes
  useEffect(() => {
    if (currentMarkerList && currentMarkerList.length >= 0) {
      setMarkerList(currentMarkerList)
    }
  }, [current360Id, currentMarkerList]) // Use memoized ID and marker list

  // Simplified parent state sync - only for position updates from Leva controls
  // Add/remove operations now update parent state immediately in their handlers
  useEffect(() => {
    if (!_360Object || typeof set_360Object !== 'function') return

    // Clear any existing timeout
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current)
    }

    // Only sync if this is a position update (not add/remove which are handled immediately)
    debounceTimeoutRef.current = setTimeout(() => {
      const currentObjectMarkers = _360Object.markerList || []
      const hasPositionChanges = markerList.some((marker, index) => {
        const currentMarker = currentObjectMarkers[index]
        return currentMarker && (
          currentMarker.x !== marker.x ||
          currentMarker.y !== marker.y ||
          currentMarker.z !== marker.z
        )
      })

      if (hasPositionChanges && JSON.stringify(currentObjectMarkers) !== JSON.stringify(markerList)) {
        set_360Object(prev => ({
          ...prev,
          markerList: [...markerList] // Create new array to ensure immutability
        }))
      }
    }, 100) // 100ms debounce

    // Cleanup timeout on unmount
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current)
      }
    }
  }, [markerList, _360Object, set_360Object])

  // Clear status messages after 5 seconds
  useEffect(() => {
    if (submitStatus.message) {
      const timer = setTimeout(() => {
        setSubmitStatus({ type: '', message: '' })
      }, 5000)
      return () => clearTimeout(timer)
    }
  }, [submitStatus.message])

  // Enhanced handleSubmit function with comprehensive error handling and user feedback
  const handleSubmit = useCallback(async () => {
    if (!_360Object?._id) {
      setSubmitStatus({
        type: 'error',
        message: 'No 360° image selected. Please select an image first.'
      })
      return
    }

    setIsSubmitting(true)
    setSubmitStatus({ type: 'loading', message: 'Saving changes...' })

    try {
      // Prepare the payload with camera settings, marker data, and name if changed
      const payload = {
        cameraPosition: _360Object.cameraPosition,
        _360Rotation: _360Object._360Rotation,
        markerList: markerList
      }

      // Include name field if it exists in _360Object (for name updates)
      if (_360Object.name) {
        payload.name = _360Object.name
      }

      // console.log('Submitting payload:', payload)

      const response = await fetch(`/api/360s/${_360Object._id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      })

      const result = await response.json()

      if (!response.ok) {
        // Handle HTTP error responses
        if (response.status === 400) {
          setSubmitStatus({
            type: 'error',
            message: `Validation Error: ${result.message || 'Invalid data provided'}`
          })
        } else if (response.status === 401) {
          setSubmitStatus({
            type: 'error',
            message: 'Authentication required. Please sign in and try again.'
          })
        } else if (response.status === 403) {
          setSubmitStatus({
            type: 'error',
            message: 'Permission denied. You need manager/admin access to save changes.'
          })
        } else if (response.status === 404) {
          setSubmitStatus({
            type: 'error',
            message: '360° image not found. It may have been deleted.'
          })
        } else if (response.status >= 500) {
          setSubmitStatus({
            type: 'error',
            message: 'Server error. Please try again later.'
          })
        } else {
          setSubmitStatus({
            type: 'error',
            message: `Error: ${result.message || 'Unknown error occurred'}`
          })
        }
        return
      }

      if (result.success) {
        // console.log('360° data updated successfully:', result.data)

        // Update the parent component with the fresh database data
        if (typeof set_360Object === 'function') {
          set_360Object(result.data)
        }

        // Trigger a refresh of the parent component's data
        if (typeof window !== 'undefined' && window.refreshDashboardData) {
          window.refreshDashboardData()
        }

        setSubmitStatus({
          type: 'success',
          message: 'Changes saved successfully! Camera settings and markers updated.'
        })
      } else {
        setSubmitStatus({
          type: 'error',
          message: `Save failed: ${result.message || 'Unknown error'}`
        })
      }
    } catch (error) {
      console.error('Error submitting 360° data:', error)

      // Handle different types of errors
      if (error.name === 'TypeError' && error.message.includes('fetch')) {
        setSubmitStatus({
          type: 'error',
          message: 'Network error. Please check your connection and try again.'
        })
      } else if (error.name === 'SyntaxError') {
        setSubmitStatus({
          type: 'error',
          message: 'Server response error. Please try again.'
        })
      } else {
        setSubmitStatus({
          type: 'error',
          message: `Unexpected error: ${error.message}`
        })
      }
    } finally {
      setIsSubmitting(false)
    }
  }, [_360Object, markerList, set_360Object])

  // Memoize the delete marker function with immediate parent state update
  const handleDeleteMarker = useCallback((markerName) => {
    const updatedMarkerList = markerList.filter(item => item?.name !== markerName)
    setMarkerList(updatedMarkerList)

    // Immediately update parent state for real-time UI sync
    if (typeof set_360Object === 'function') {
      set_360Object(prev => ({
        ...prev,
        markerList: updatedMarkerList
      }))
    }
  }, [markerList, set_360Object])

  // Memoize the update marker function with improved debouncing and parent state sync
  const handleUpdateMarker = useCallback((markerName, updates) => {
    // Immediately update the UI for better responsiveness
    const updatedMarkerList = markerList.map(item =>
      item?.name === markerName ? { ...item, ...updates } : item
    )
    setMarkerList(updatedMarkerList)

    // Immediately update parent state for real-time UI sync
    if (typeof set_360Object === 'function') {
      set_360Object(prev => ({
        ...prev,
        markerList: updatedMarkerList
      }))
    }
  }, [markerList, set_360Object])

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, [])

  // Memoize marker type options to prevent unnecessary re-renders
  const markerTypeOptions = useMemo(() =>
    settings.markerList.markerType.map((type, index) => (
      <option key={index} value={type}>{type}</option>
    )), []
  )

  // Memoize content type options
  const contentTypeOptions = useMemo(() =>
    settings.markerList.contentType.map((type, index) => (
      <option key={index} value={type}>{type}</option>
    )), []
  )

  // Memoize 360s list options to prevent unnecessary re-renders
  const threeSixtyOptions = useMemo(() =>
    _360sList?.map((item, index) => (
      <option key={item._id || index} value={item?.name}>{item?.name}</option>
    )) || [], [_360sList]
  )

  return (
    <div className='inputWrapper w-full h-[calc(100%-100px)]'>
      <div className='w-full h-fit flex flex-col bg-gray-100 rounded-md shadow p-2 border-1 border-gray-200'>
        <span className='text-sm text-gray-600 font-medium'>Marker title</span>
        <div className='flex items-center h-fit gap-1 p-1 shadow-md rounded mt-2 bg-gray-300'>
          <input
            value={input.name}
            onChange={e=>setInput(prev => ({...prev, name: e.target.value}))}
            className={css}
            type="text"
            placeholder='Enter marker name...'
            maxLength={50}
          />
          <select
            value={input.markerType}
            onChange={e=>setInput(prev => ({...prev, markerType: e.target.value}))}
            className={`${css}`}
          >
            <option value="">Select marker type...</option>
            {markerTypeOptions}
          </select>
          <button
            onClick={handleAddList}
            disabled={!input.name || !input.markerType}
            className={`w-fit px-2 capitalize h-9 rounded text-xs justify-center items-center cursor-pointer shadow transition-colors ${
              !input.name || !input.markerType
                ? 'bg-gray-400 text-gray-200 cursor-not-allowed'
                : 'bg-gray-800 text-white hover:bg-gray-700'
            }`}
          >
            Add
          </button>
        </div>
      </div>
      <hr className='w-full border-1 border-gray-300 my-2'/>
      <div className='w-full h-full flex-col flex items-center gap-2 bg-gray-50 mt-2 rounded-md shadow p-2 border-1 border-gray-200'>
        <div className='flex flex-col gap-2 w-full overflow-y-auto h-full'>
          {markerList?.map((marker, index) => {
            // Create stable key using marker name and index
            const stableKey = `marker-${marker?.name || `unnamed-${index}`}-${marker?.markerType || 'no-type'}`

            return (
            <div key={stableKey} className='flex w-full h-fit flex-col p-2 bg-gray-200 rounded items-center gap-1'>
              {/* Marker data */}
              <div className='flex w-full h-7 items-center gap-2'>
                <div className='flex w-full items-center gap-6'>
                  <span className='text-xs capitalize text-gray-600'>name:</span>
                  <span className='text-xs capitalize text-gray-600 font-medium'>{marker?.name}</span>
                </div>
                <BiTrash onClick={() => handleDeleteMarker(marker?.name)} className='text-2xl cursor-pointer text-gray-400 border-1 border-gray-400 rounded h-2/3 w-fit'/>
              </div>
              {(marker?.markerType===settings.markerList.markerType[0] || marker?.markerType===settings.markerList.markerType[1] || marker?.markerType===settings.markerList.markerType[2] || marker?.markerType===settings.markerList.markerType[3])
                ? <select
                    value={marker?._360Name || ''}
                    onChange={e => handleUpdateMarker(marker?.name, { _360Name: e.target.value })}
                    className={`${css}`}
                  >
                    <option value="">Select 360 Name</option>
                    {threeSixtyOptions}
                  </select>
                : <select
                    value={marker?.infoType || ''}
                    onChange={e => handleUpdateMarker(marker?.name, { infoType: e.target.value })}
                    className={`${css}`}
                  >
                    {console.log('',{contentTypeOptions:contentTypeOptions})}
                    <option value="">Select Content Type</option>
                    {contentTypeOptions}
                  </select>
              }
            </div>
            )
          })}
        </div>

        {/* Status Message Display */}
        {submitStatus.message && (
          <div className={`w-full p-2 rounded text-xs text-center font-medium ${
            submitStatus.type === 'success'
              ? 'bg-green-100 text-green-800 border border-green-200'
              : submitStatus.type === 'error'
              ? 'bg-red-100 text-red-800 border border-red-200'
              : 'bg-blue-100 text-blue-800 border border-blue-200'
          }`}>
            {submitStatus.message}
          </div>
        )}

        {/* Submit Button with Loading State */}
        <button
          onClick={handleSubmit}
          disabled={isSubmitting}
          className={`flex h-10 w-full max-h-10 px-2 capitalize rounded text-xs justify-center items-center cursor-pointer shadow transition-colors ${
            isSubmitting
              ? 'bg-gray-400 text-gray-200 cursor-not-allowed'
              : 'bg-gray-800 text-white hover:bg-gray-700'
          }`}
        >
          {isSubmitting ? (
            <>
              <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-2"></div>
              Saving...
            </>
          ) : (
            'Save'
          )}
        </button>
      </div>
    </div>
  )
}

// Export memoized component to prevent unnecessary re-renders
export default memo(MarkersInputList);
