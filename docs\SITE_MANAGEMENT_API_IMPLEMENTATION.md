# Site Management API Implementation

## Overview
Implemented a comprehensive site management system with MongoDB Site model and API endpoints, integrated with the SettingsManagementDashboard for managing menu link assignments.

## Components Implemented

### **1. MongoDB Site Model** 
**File**: `src/models/Site.js`

#### **Schema Features**
- **Required menulinks object field** with validation
- Site configuration settings
- Landing page settings
- Marker settings
- Version control and rollback capability
- Automatic backup of previous settings

#### **Key Fields**
```javascript
{
  name: String (required),
  menulinks: {
    home: Array,
    entrance: Array, 
    firstFloor: Array,
    outDoors: Array,
    campOutskirts: Array
  } (required),
  configuration: Object,
  landingPageSettings: Object,
  markerSettings: Object,
  isActive: Boolean,
  version: String,
  lastModifiedBy: ObjectId,
  previousSettings: Object
}
```

#### **Static Methods**
- `getOrCreateDefault()` - Gets or creates default site settings
- Instance method `updateMenuLinks()` - Updates menu links with validation

### **2. Site Management API Endpoint**
**File**: `src/app/api/site-management/route.js`

#### **Endpoints**
- **GET** `/api/site-management` - Get current site settings (manager/admin)
- **POST** `/api/site-management` - Create new site settings (manager/admin)
- **PUT** `/api/site-management` - Update site settings (manager/admin)
- **DELETE** `/api/site-management` - Reset to default (admin only)

#### **Authentication**
- Uses `requireManagerAPI` middleware
- Manager/Admin access for GET, POST, PUT
- Admin-only access for DELETE operations

#### **Validation**
- Required menulinks object validation
- Array validation for menu categories
- Comprehensive error handling
- Rollback capability

### **3. Enhanced SettingsManagementDashboard**
**File**: `src/components/settings/SettingsManagementDashboard.jsx`

#### **New Features**
- **State Management**: Integrated site settings and menu links state
- **API Integration**: Fetches and saves data to site-management endpoint
- **Real-time Updates**: Menu link changes update state immediately
- **Error/Success Handling**: User feedback for save operations
- **Loading States**: Proper loading indicators during operations

#### **Component Structure**
```javascript
// State Management
const [siteSettings, setSiteSettings] = useState(null);
const [menuLinks, setMenuLinks] = useState({
  home: [],
  entrance: [],
  firstFloor: [],
  outDoors: [],
  campOutskirts: []
});

// API Functions
fetchSiteSettings() // Loads current settings
handleSave() // Saves changes to API
handleMenuLinkChange() // Updates local state
```

### **4. Middleware Protection**
**File**: `src/middleware.js`

#### **Route Protection Added**
```javascript
'/api/site-management': {
  GET: ['admin', 'manager'],
  POST: ['admin', 'manager'], 
  PUT: ['admin', 'manager'],
  DELETE: ['admin'], // Only admin can reset
}
```

## API Usage Examples

### **Get Site Settings**
```javascript
GET /api/site-management

Response:
{
  "success": true,
  "data": {
    "name": "Elephant Island Lodge",
    "menulinks": {
      "home": [],
      "entrance": ["entrance_360", "livingroom_001"],
      "firstFloor": ["bedroom_1", "bedroom_2"],
      "outDoors": ["terrace", "west_view"],
      "campOutskirts": []
    },
    "configuration": {...},
    "isActive": true
  }
}
```

### **Update Menu Links**
```javascript
PUT /api/site-management
Content-Type: application/json

{
  "menulinks": {
    "home": ["home_360"],
    "entrance": ["entrance_360", "livingroom_001", "dining_001"],
    "firstFloor": ["bedroom_1", "bedroom_2", "master_bedroom"],
    "outDoors": ["terrace", "west_view", "east_view"],
    "campOutskirts": ["camp_site"]
  }
}

Response:
{
  "success": true,
  "data": {...},
  "message": "Site settings updated successfully"
}
```

## Dashboard Integration

### **Menu Link Assignment Flow**
1. **Load Data**: Dashboard fetches 360° files and site settings on mount
2. **Display Options**: Shows dropdown menus for each menu button
3. **User Selection**: User selects 360° links for each menu item
4. **State Update**: Changes update local state immediately
5. **Save Changes**: User clicks "Save Changes" to persist to database
6. **Feedback**: Success/error messages displayed to user

### **Component Props Flow**
```javascript
<MenuInputFeilds
  item={menuItem}           // Menu item configuration
  _360Files={_360Files}     // Available 360° files
  value={currentValue}      // Current selected value
  onChange={handleChange}   // Change handler
  category="entrance"       // Menu category
  index={0}                // Item index
/>
```

## Error Handling

### **API Level**
- Validation errors with detailed messages
- Authentication/authorization errors
- Database connection errors
- Rollback capability on failures

### **Dashboard Level**
- Network error handling
- User-friendly error messages
- Loading state management
- Success confirmation messages

## Security Features

### **Access Control**
- Manager/Admin role requirements
- Admin-only reset functionality
- JWT token validation
- Rate limiting protection

### **Data Validation**
- Required field validation
- Type checking for menulinks object
- Array validation for menu categories
- Sanitization of input data

## Database Design

### **Collection Structure**
- Single active site configuration
- Automatic versioning
- Previous settings backup
- Indexed for performance

### **Relationships**
- References to User model for lastModifiedBy
- Integration with existing 360° files
- Compatible with current settings structure

## Testing

### **API Testing**
```bash
# Test GET endpoint
curl -H "Authorization: Bearer <token>" \
     https://localhost:3001/api/site-management

# Test PUT endpoint  
curl -X PUT \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer <token>" \
     -d '{"menulinks":{"home":["test"]}}' \
     https://localhost:3001/api/site-management
```

### **Dashboard Testing**
1. Load dashboard and verify 360° files load
2. Select different 360° links for menu items
3. Save changes and verify success message
4. Refresh page and verify selections persist
5. Test error scenarios (network issues, validation errors)

## Future Enhancements

### **Planned Features**
- Bulk menu link operations
- Menu link validation against available 360° files
- Preview functionality before saving
- Audit trail for changes
- Import/export site configurations

### **Performance Optimizations**
- Caching of site settings
- Optimistic updates
- Debounced save operations
- Lazy loading of 360° files

## Deployment Notes

### **Database Migration**
- No migration required - creates default settings automatically
- Backward compatible with existing data
- Graceful fallback to default settings

### **Environment Variables**
- No new environment variables required
- Uses existing MongoDB connection
- Leverages existing authentication system

## Troubleshooting

### **Common Issues**
1. **403 Forbidden**: Check user role (manager/admin required)
2. **Validation Errors**: Ensure menulinks object has required arrays
3. **Loading Issues**: Verify 360° API is accessible
4. **Save Failures**: Check network connectivity and authentication

### **Debug Commands**
```bash
# Check site settings in MongoDB
db.site_settings.find({isActive: true})

# Verify API endpoint
curl -I https://localhost:3001/api/site-management

# Check middleware logs
tail -f logs/middleware.log
```
