'use client';

import { useState, useEffect, useRef, Suspense, useMemo } from 'react';
import { Canvas } from '@react-three/fiber';
import { useRouter } from 'next/navigation';
import PanoramicSphere from './PanoramicSphere';
import LoadingOverlay from './LoadingOverlay';
import FadeTransition from './FadeTransition';
import _360InfoMarkers from './_360InfoMarkers';
import { useContextExperience } from '@/contexts/useContextExperience';
import { ACTIONS_EXPERIENCE_STATE } from '@/contexts/reducerExperience';
// import { sortedArray } from 'three/src/animation/AnimationUtils'; // This import seems unused and might cause issues if not a valid module

export default function ThreeSixtyViewer({id}) {
  const router = useRouter();
  const [threeSixties, setThreeSixties] = useState([]);
  // Initialize _360Object with default structure to avoid undefined errors
  const [_360Object, set_360Object] = useState({
    cameraPosition: 0,
    _360Rotation: 0,
    markerList: [],
  });
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [resetView, setResetView] = useState(false);
  const [textureCache, setTextureCache] = useState(new Map());
  const [loadingQueue, setLoadingQueue] = useState([]);
  const [error, setError] = useState(null);
  const containerRef = useRef(null);
  const {experienceState,disptachExperience}=useContextExperience()

  // Derive currentImage from threeSixties array
  const currentImage = useMemo(() => {
    const image = threeSixties.find(item => item.name === id);
    return image;
  }, [threeSixties, id]);


  // Effect to update _360Object when currentImage changes
  useEffect(() => {
    const new_360ObjectState = {
      cameraPosition: currentImage?.cameraPosition || 0,
      _360Rotation: currentImage?._360Rotation || 0,
      markerList: currentImage?.markerList || [],
    };
    set_360Object(new_360ObjectState);

    let timer;
    if (currentImage) {
      setResetView(true);
      timer = setTimeout(() => setResetView(false), 100);
    }
    return () => {
      if (timer) {
        clearTimeout(timer);
      }
    };
  }, [currentImage, set_360Object]);


  // Fetch 360° images from API
  useEffect(() => {
    fetchThreeSixties();
    disptachExperience({type:ACTIONS_EXPERIENCE_STATE.RESET});
  }, []);

  // --- Moved handleImageChange and toggleFullscreen definitions here ---
  const handleImageChange = async (index) => {
    if (index === currentImageIndex || isTransitioning) return;

    setIsTransitioning(true);

    // Wait for fade out
    await new Promise(resolve => setTimeout(resolve, 500));

    setCurrentImageIndex(index);

    // Wait for fade in
    await new Promise(resolve => setTimeout(resolve, 500));

    setIsTransitioning(false);
  };


  // --- End of moved definitions ---


  // Handle keyboard controls
  useEffect(() => {
    const handleKeyDown = (event) => {
      switch (event.key) {
        case 'ArrowLeft':
          event.preventDefault();
          if (currentImageIndex > 0) {
            handleImageChange(currentImageIndex - 1);
          }
          break;
        case 'ArrowRight':
          event.preventDefault();
          if (currentImageIndex < threeSixties.length - 1) {
            handleImageChange(currentImageIndex + 1);
          }
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [currentImageIndex, threeSixties.length, handleImageChange]);

  const fetchThreeSixties = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch('/api/360s?sort=priority&order=asc&limit=50');
      const data = await response.json();

      if (data.success && data.data.length > 0) {
        const sortedData = data.data.sort((b, a) => (a.priority || 0) - (b.priority || 0));
        setThreeSixties(sortedData);

        setCurrentImageIndex(0);
        initializeTextureLoading(sortedData);
      } else {
        setError('No 360° images found');
      }
    } catch (err) {
      console.error('Error fetching 360° images:', err);
      setError('Failed to load 360° images');
    } finally {
      setIsLoading(false);
    }
  };

  const initializeTextureLoading = (images) => {
    const sortedImages = [...images].sort((b, a) => (a.priority || 0) - (b.priority || 0));

    setLoadingQueue(sortedImages.map((img, index) => ({
      ...img,
      originalIndex: images.findIndex(item => item._id === img._id),
      priority: img.priority || 0,
      loadOrder: index,
      status: 'pending'
    })));
  };

  const handleBack = () => {
    router.push('/');
  };

  // Memoize the props object specifically for PanoramicSphere
  const panoSphereProps = useMemo(() => {
    return {
      currentImage: currentImage,
      imageUrl: currentImage?.url,
      imageId: currentImage?._id,
      textureCache: textureCache,
      setTextureCache: setTextureCache,
      loadingQueue: loadingQueue,
      setLoadingQueue: setLoadingQueue,
      _360Object: _360Object, // Pass the actual _360Object state
      set_360Object: set_360Object,
      onTextureLoad: () => { /* Texture loaded successfully */ },
      resetView: resetView,
    };
  }, [
    currentImage,
    textureCache,
    setTextureCache,
    loadingQueue,
    setLoadingQueue,
    _360Object, // Dependency for _360Object state
    resetView,
  ]);

  // Memoize the props object specifically for _360InfoMarkers
  const infoMarkersProps = useMemo(() => {
    return {
      markerList: _360Object?.markerList || [],
    };
  }, [_360Object]);


  if (isLoading) {
    return <LoadingOverlay message="Loading 360° images..." />;
  }

  if (error) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center text-white">
          <h2 className="text-2xl font-bold mb-4">Error Loading 360° Viewer</h2>
          <p className="text-gray-300 mb-6">{error}</p>
          <button
            onClick={handleBack}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors"
          >
            Back to Home
          </button>
        </div>
      </div>
    );
  }

  if (threeSixties.length === 0) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center text-white">
          <h2 className="text-2xl font-bold mb-4">No 360° Images Available</h2>
          <p className="text-gray-300 mb-6">Upload some 360° images to get started.</p>
          <button
            onClick={handleBack}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors"
          >
            Back to Manager
          </button>
        </div>
      </div>
    );
  }

  return (
    <div 
      ref={containerRef}
      className="relative w-full h-screen bg-black overflow-hidden"
    >
      {/* 3D Canvas */}
      <Canvas
        camera={{ position: [0, 0, 0.1], fov: 75 }}
        className="w-full h-full"
        gl={{
          antialias: true,
          alpha: false,
          preserveDrawingBuffer: false
        }}
      >
        <Suspense fallback={null}>
          {currentImage && ( // Only render PanoramicSphere if currentImage is available
            <PanoramicSphere {...panoSphereProps} />
          )}
          {/* Render _360InfoMarkers only if _360Object.markerList is available */}
          {_360Object?.markerList && (
            <_360InfoMarkers {...infoMarkersProps} />
          )}
        </Suspense>
      </Canvas>

      {/* Fade Transition Overlay */}
      <FadeTransition isTransitioning={isTransitioning} />

      {/* Loading Overlay for texture loading */}
      {isLoading && (
        <LoadingOverlay message="Loading textures..." />
      )}
    </div>
  );
}
