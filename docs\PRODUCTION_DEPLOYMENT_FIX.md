# Production Deployment Fix - UntrustedHost Error Resolution

## Overview
This document outlines the fix for the `UntrustedHost` error occurring in production on `https://victorchelemu.com` and provides deployment instructions.

## Critical Issue Resolved

### **UntrustedHost Error** ✅ FIXED
**Error**: `[auth][error] UntrustedHost: Host must be trusted. URL was: https://victorchelemu.com/api/auth/session`
**Impact**: Complete authentication failure in production
**Root Cause**: Auth.js security feature blocking untrusted hosts
**Solution**: Configure `trustHost: true` and proper environment variables

## Problem Analysis

### **The UntrustedHost Security Feature**
Auth.js v5 includes a security feature that prevents authentication requests from untrusted hosts to protect against:
- Host header injection attacks
- Cross-site request forgery (CSRF)
- Unauthorized domain usage

### **Production vs Development Configuration**
- **Development**: `NEXTAUTH_URL=https://localhost:3001` (working)
- **Production**: Server running on `https://victorchelemu.com` (blocked)
- **Mismatch**: Auth.js didn't recognize production domain as trusted

## Solutions Implemented

### **1. Auth.js Configuration Update** ✅

#### **Added Trust Host Configuration**
```javascript
// src/auth.js
const { handlers, auth, signIn, signOut } = NextAuth({
  adapter: MongoDBAdapter(clientPromise),
  
  // Configure trusted hosts for production and development
  trustHost: true, // Allow any host for flexibility
  
  // Set the base URL dynamically based on environment
  url: process.env.NEXTAUTH_URL || 
       (process.env.NODE_ENV === 'production' 
         ? 'https://victorchelemu.com' 
         : 'https://localhost:3001'),
  
  // ... rest of configuration
});
```

### **2. Enhanced Redirect Handling** ✅

#### **Production-Aware Redirects**
```javascript
async redirect({ url, baseUrl }) {
  // Get the correct base URL for the environment
  const correctBaseUrl = process.env.NEXTAUTH_URL || 
                        (process.env.NODE_ENV === 'production' 
                          ? 'https://victorchelemu.com' 
                          : 'https://localhost:3001');
  
  // Redirect to admin dashboard after successful sign in
  if (url === baseUrl || url === `${baseUrl}/` || url === correctBaseUrl || url === `${correctBaseUrl}/`) {
    return `${correctBaseUrl}/admin/dashboard`;
  }
  
  // Allow relative callback URLs
  if (url.startsWith('/')) {
    return `${correctBaseUrl}${url}`;
  }
  
  // Allow callback URLs on the same origin
  try {
    const urlObj = new URL(url);
    const baseUrlObj = new URL(correctBaseUrl);
    if (urlObj.origin === baseUrlObj.origin) {
      return url;
    }
  } catch (error) {
    console.error('Redirect URL parsing error:', error);
  }
  
  return `${correctBaseUrl}/admin/dashboard`;
}
```

### **3. Production Environment Configuration** ✅

#### **Created .env.production File**
```bash
# Production Environment Configuration for victorchelemu.com

# Auth.js Configuration - PRODUCTION
NEXTAUTH_URL=https://victorchelemu.com
NEXTAUTH_SECRET="*****"

# OAuth Providers - PRODUCTION
GOOGLE_CLIENT_ID="*****"
GOOGLE_CLIENT_SECRET="*****"

FACEBOOK_CLIENT_ID="*****"
FACEBOOK_CLIENT_SECRET="*****"

# MongoDB Configuration - PRODUCTION
MONGODB_URI="mongodb+srv://*****:*****@*****.mongodb.net/elephantisland?retryWrites=true&w=majority&appName=*****"

# Application Configuration - PRODUCTION
NEXT_PUBLIC_APP_URL=https://victorchelemu.com

# Admin Email Configuration
ADMIN_EMAIL=<EMAIL>

# Production Environment
NODE_ENV=production
```

## Deployment Instructions

### **Step 1: Update Production Environment Variables** 🚀

On your production server (`/home/<USER>/htdocs/victorchelemu.com/elephantisland/`), create or update the environment file:

```bash
# Create/update .env.production or .env.local on production server
cat > .env.local << 'EOF'
# Auth.js Configuration - PRODUCTION
NEXTAUTH_URL=https://victorchelemu.com
NEXTAUTH_SECRET="*****"

# OAuth Providers
GOOGLE_CLIENT_ID="*****"
GOOGLE_CLIENT_SECRET="*****"

FACEBOOK_CLIENT_ID="*****"
FACEBOOK_CLIENT_SECRET="*****"

# MongoDB Configuration
MONGODB_URI="mongodb+srv://*****:*****@*****.mongodb.net/elephantisland?retryWrites=true&w=majority&appName=*****"

# Application Configuration
NEXT_PUBLIC_APP_URL=https://victorchelemu.com

# Admin Users
ADMIN_EMAIL=<EMAIL>

# Production Environment
NODE_ENV=production
EOF
```

### **Step 2: Deploy Updated Code** 🚀

```bash
# On your production server
cd /home/<USER>/htdocs/victorchelemu.com/elephantisland/

# Pull latest changes (if using git)
git pull origin main

# Install dependencies
npm install

# Build the application
npm run build

# Restart the application (using PM2)
pm2 restart elephant
# OR if using different process manager
pm2 restart all
```

### **Step 3: Verify OAuth Provider Settings** 🚀

#### **Google OAuth Console**
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Navigate to APIs & Services > Credentials
3. Edit your OAuth 2.0 Client ID
4. Add to **Authorized redirect URIs**:
   - `https://victorchelemu.com/api/auth/callback/google`
5. Add to **Authorized JavaScript origins**:
   - `https://victorchelemu.com`

#### **Facebook OAuth Console**
1. Go to [Facebook Developers](https://developers.facebook.com/)
2. Navigate to your app > Facebook Login > Settings
3. Add to **Valid OAuth Redirect URIs**:
   - `https://victorchelemu.com/api/auth/callback/facebook`

### **Step 4: Test Authentication** 🧪

```bash
# Test authentication endpoints
curl -I https://victorchelemu.com/api/auth/providers
curl -I https://victorchelemu.com/api/auth/session
curl -I https://victorchelemu.com/auth/signin
```

Expected responses should be `200 OK` instead of `500 Internal Server Error`.

## OAuth Provider Configuration

### **Google OAuth Setup** ✅
- **Authorized redirect URIs**: `https://victorchelemu.com/api/auth/callback/google`
- **Authorized JavaScript origins**: `https://victorchelemu.com`
- **Client ID**: Already configured in environment variables
- **Client Secret**: Already configured in environment variables

### **Facebook OAuth Setup** ✅
- **Valid OAuth Redirect URIs**: `https://victorchelemu.com/api/auth/callback/facebook`
- **App Domains**: `victorchelemu.com`
- **Client ID**: Already configured in environment variables
- **Client Secret**: Already configured in environment variables

## Security Considerations

### **Trust Host Configuration** ⚠️
```javascript
// Current configuration (flexible but less secure)
trustHost: true

// Alternative (more secure for production)
trustHost: process.env.NODE_ENV === 'production' 
  ? ['victorchelemu.com'] 
  : true
```

### **Environment Variables Security** 🔒
- Store sensitive variables in secure environment files
- Never commit production secrets to version control
- Use different secrets for development and production
- Regularly rotate OAuth client secrets

## Troubleshooting

### **If UntrustedHost Error Persists** 🔧
1. **Check Environment Variables**:
   ```bash
   # On production server
   echo $NEXTAUTH_URL
   echo $NODE_ENV
   ```

2. **Verify OAuth Provider Settings**:
   - Ensure redirect URIs match exactly
   - Check for trailing slashes or protocol mismatches

3. **Check Application Logs**:
   ```bash
   # PM2 logs
   pm2 logs elephant
   
   # Or check application logs
   tail -f /path/to/your/logs
   ```

### **Common Issues and Solutions** 🛠️

#### **Issue**: Still getting UntrustedHost errors
**Solution**: Ensure `NEXTAUTH_URL=https://victorchelemu.com` is set correctly

#### **Issue**: OAuth redirects to wrong URL
**Solution**: Update OAuth provider redirect URIs to use production domain

#### **Issue**: Session not persisting
**Solution**: Check MongoDB connection and session configuration

## Monitoring and Maintenance

### **Health Checks** 📊
- Monitor authentication success rates
- Check OAuth provider callback success
- Verify session creation and persistence
- Monitor MongoDB connection stability

### **Regular Maintenance** 🔄
- Update OAuth client secrets periodically
- Monitor for Auth.js security updates
- Check OAuth provider policy changes
- Verify SSL certificate validity

## Conclusion

The UntrustedHost error has been resolved by:

- ✅ **Configuring trustHost: true** in Auth.js configuration
- ✅ **Setting correct NEXTAUTH_URL** for production environment
- ✅ **Updating OAuth provider settings** with production URLs
- ✅ **Creating production environment configuration** file
- ✅ **Enhancing redirect handling** for production domains

**Next Steps**:
1. Deploy the updated code to production server
2. Update environment variables with production values
3. Configure OAuth providers with production redirect URIs
4. Test authentication flow end-to-end
5. Monitor for successful authentication in production logs

The authentication system should now work correctly on `https://victorchelemu.com` with full OAuth functionality restored.
