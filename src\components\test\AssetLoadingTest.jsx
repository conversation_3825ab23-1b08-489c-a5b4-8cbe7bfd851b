'use client';

import React, { useState, useEffect } from 'react';
import { loadImageWithCache, getCacheStats } from '@/lib/asset-loader';
import ImageWrapperResponsive from '@/components/ImageWrapperResponsive';

export default function AssetLoadingTest() {
  const [testResults, setTestResults] = useState({
    loading: false,
    success: false,
    error: null,
    cacheStats: null,
  });

  const testAssets = [
    '/assets/elephant_island_logo.png',
    '/assets/book_btn_off.png',
    '/assets/book_btn_ov.png',
    '/assets/guide_btn_off.png',
    '/assets/guide_btn_ov.png',
  ];

  const runAssetLoadingTest = async () => {
    setTestResults(prev => ({ ...prev, loading: true, error: null }));

    try {
      console.log('Starting asset loading test...');
      
      // Test loading multiple assets
      const loadPromises = testAssets.map(async (src, index) => {
        console.log(`Loading asset ${index + 1}/${testAssets.length}: ${src}`);
        
        try {
          const image = await loadImageWithCache(src, {
            maxRetries: 2,
            retryDelay: 500,
            timeout: 5000,
          });
          
          console.log(`✅ Successfully loaded: ${src}`, {
            width: image.width,
            height: image.height,
            crossOrigin: image.crossOrigin,
          });
          
          return { src, success: true, image };
        } catch (error) {
          console.error(`❌ Failed to load: ${src}`, error);
          return { src, success: false, error: error.message };
        }
      });

      const results = await Promise.allSettled(loadPromises);
      const successCount = results.filter(r => r.status === 'fulfilled' && r.value.success).length;
      
      console.log(`Asset loading test completed: ${successCount}/${testAssets.length} successful`);
      
      // Get cache statistics
      const cacheStats = getCacheStats();
      console.log('Cache statistics:', cacheStats);

      setTestResults({
        loading: false,
        success: successCount === testAssets.length,
        error: successCount < testAssets.length ? `Only ${successCount}/${testAssets.length} assets loaded successfully` : null,
        cacheStats,
        results: results.map(r => r.status === 'fulfilled' ? r.value : { error: r.reason }),
      });

    } catch (error) {
      console.error('Asset loading test failed:', error);
      setTestResults({
        loading: false,
        success: false,
        error: error.message,
        cacheStats: getCacheStats(),
      });
    }
  };

  useEffect(() => {
    // Auto-run test on component mount
    runAssetLoadingTest();
  }, []);

  return (
    <div className="p-6 bg-white rounded-lg shadow-lg max-w-4xl mx-auto">
      <h2 className="text-2xl font-bold mb-4">Asset Loading Test</h2>
      
      {/* Test Status */}
      <div className="mb-6">
        <div className={`p-4 rounded-lg ${
          testResults.loading ? 'bg-blue-100 text-blue-800' :
          testResults.success ? 'bg-green-100 text-green-800' :
          testResults.error ? 'bg-red-100 text-red-800' :
          'bg-gray-100 text-gray-800'
        }`}>
          {testResults.loading && '🔄 Testing asset loading...'}
          {!testResults.loading && testResults.success && '✅ All assets loaded successfully!'}
          {!testResults.loading && testResults.error && `❌ ${testResults.error}`}
        </div>
      </div>

      {/* Test Controls */}
      <div className="mb-6">
        <button
          onClick={runAssetLoadingTest}
          disabled={testResults.loading}
          className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg transition-colors"
        >
          {testResults.loading ? 'Testing...' : 'Run Test Again'}
        </button>
      </div>

      {/* Cache Statistics */}
      {testResults.cacheStats && (
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-2">Cache Statistics</h3>
          <div className="bg-gray-50 p-4 rounded-lg">
            <p><strong>Cache Size:</strong> {testResults.cacheStats.cacheSize} items</p>
            <p><strong>Currently Loading:</strong> {testResults.cacheStats.loadingCount} items</p>
            {testResults.cacheStats.cacheEntries.length > 0 && (
              <div className="mt-2">
                <strong>Cached Assets:</strong>
                <ul className="list-disc list-inside ml-4 text-sm">
                  {testResults.cacheStats.cacheEntries.map((entry, index) => (
                    <li key={index} className="text-gray-600">{entry}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Visual Test - Display Assets */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-2">Visual Asset Test</h3>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
          {testAssets.map((src, index) => (
            <div key={index} className="text-center">
              <div className="w-20 h-20 mx-auto mb-2 border border-gray-300 rounded-lg overflow-hidden">
                <ImageWrapperResponsive
                  src={src}
                  alt={`Test asset ${index + 1}`}
                  className="w-full h-full object-contain"
                  showLoadingSpinner={true}
                  onLoad={() => console.log(`Visual test: ${src} loaded`)}
                  onError={(error) => console.error(`Visual test: ${src} failed`, error)}
                />
              </div>
              <p className="text-xs text-gray-600 break-all">{src.split('/').pop()}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Detailed Results */}
      {testResults.results && (
        <div>
          <h3 className="text-lg font-semibold mb-2">Detailed Results</h3>
          <div className="bg-gray-50 p-4 rounded-lg max-h-64 overflow-y-auto">
            <pre className="text-sm">
              {JSON.stringify(testResults.results, null, 2)}
            </pre>
          </div>
        </div>
      )}
    </div>
  );
}
