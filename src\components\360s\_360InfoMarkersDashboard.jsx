'use client'
import { Html } from '@react-three/drei'
import React, { useState, useEffect, useRef, useMemo } from 'react'
// import { useRouter } from 'next/navigation'
import Link from 'next/link'
import ImageScalerComponent from '../ImageScalerComponent'
import { settings } from '@/lib/settings'
import { useControls } from 'leva'
import { useIntersect } from '@react-three/drei'

function IconGuides({ item, icon}) {
  const refLink = useRef(null)
  const [onHover, setOnHover] = useState(false)
  
  // console.log('IconGuides',item)
  return (
    <Link ref={refLink} href={`/360s?id=${item?._360Name}`}
      // onClick={e=>router.push(`/beta/${icon?._360Name}`)}
      onMouseEnter={() => setOnHover(!onHover)}
      onMouseLeave={() => setOnHover(!onHover)}
      className="flex cursor-pointer relative w-fit h-fit items-center justify-center"
    >
        {!onHover
            ? <ImageScalerComponent src={icon?.btnIcons?.off} alt="marker icon" />
            : <ImageScalerComponent src={icon?.btnIcons?.ov} alt="marker icon" />
        }
    </Link>
  )
}

// Create a simpler component that directly sets position without useFrame
const MarkerPosition = ({ position, children }) => {
  // const refGroup = useRef(null)
  const [markerVisble, setMarkerVisble] = useState(false)
  const refGroup = useIntersect((visible)=>setMarkerVisble(visible)) // `visible` will be true if intersecting
  // Ensure position values are numbers with fallbacks
  const x = typeof position.x === 'number' ? position.x : 0
  const y = typeof position.y === 'number' ? position.y : 0
  const z = typeof position.z === 'number' ? position.z : 0
  
  // console.log('MarkerPosition is marker visble:',markerVisble)

  // Just use the position prop directly on the group
  return (
    <group visible={markerVisble} ref={refGroup} position={[x, y, z]}>
      {children}
    </group>
  )
}

const MarkerIcon = ({ item, set_360Object }) => {
  const [onHover, setOnHover] = useState(false)
  const debounceTimeoutRef = useRef(null)

  // Ensure item is an object
  const safeItem = item || {}

  const options = useMemo(() => {
      return {
        x: {
          value: item.x || 0,
          min: -32,
          max: 32,
          step: 0.0001,
          transient: false // Disable transient updates for better performance
        },
        y: {
          value: item.y || 0,
          min: -32,
          max: 32,
          step: 0.0001,
          transient: false
        },
        z: {
          value: item.z || 0,
          min: -32,
          max: 32,
          step: 0.0001,
          transient: false
        }
      }
    }, [item.x, item.y, item.z])

  const controls = useControls(`Marker: ${safeItem.name || 'unnamed'}`, options)

  useEffect(() => {
    // set_360Object({..._360Object,...controls})
  }, [controls])
    
  // Convert item position to a proper object with numeric values
  // Use parseFloat and handle NaN values with || 0
  const position = {
    x: isNaN(parseFloat(controls.x)) ? 0 : parseFloat(controls.x),
    y: isNaN(parseFloat(controls.y)) ? 0 : parseFloat(controls.y),
    z: isNaN(parseFloat(controls.z)) ? 0 : parseFloat(controls.z)
  }

  // Throttled position updates to reduce message handler load
  useEffect(() => {
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    debounceTimeoutRef.current = setTimeout(() => {
      set_360Object(prev => ({
        ...prev,
        markerList: prev.markerList.map(m =>
          m.name === safeItem.name ? {...m, ...position} : m
        )
      }))
    }, 100); // 100ms throttle for marker position updates

    // Cleanup on unmount
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, [position, safeItem.name, set_360Object])
  

  // Debug output removed to reduce console noise
   if (process.env.NODE_ENV === 'development') {
    // console.log('Current marker list:', markerList)
    // console.log('Current input state:', input)
    // console.log('Current 360 object:', _360Object)
  }

  return (
    <MarkerPosition position={position}>
      <Html center zIndexRange={[1, 10]}>
        <div
          onPointerOver={() => setOnHover(!onHover)}
          onPointerOut={() => setOnHover(!onHover)}
          className='flex w-fit cursor-pointer h-fit items-center justify-center'
          style={{ pointerEvents: 'auto' }}
        >
          {safeItem.markerType === 'landingPage'
            ? <Link href={`/360s?id=livingroom_001`}>{settings.markerList.markerTypeIcons.landingPage.btnIcons.off}</Link> :
            safeItem.markerType === 'guide'
              ? <IconGuides 
                  // id={id} 
                  item={item} 
                  icon={settings.markerList.markerTypeIcons.guide} 
                />
              : safeItem.markerType === 'upstairs'
                ? <IconGuides 
                    // id={id} 
                    item={item} 
                    icon={settings.markerList.markerTypeIcons.upstairs} 
                  />
              : safeItem.markerType === 'downstairs'
                ? <IconGuides 
                    // id={id} 
                    item={item} 
                    icon={settings.markerList.markerTypeIcons.downstairs} 
                  />
              : safeItem.markerType === 'infoVideo'
                ? <IconGuides 
                    // id={id} 
                    item={item} 
                    icon={settings.markerList.markerTypeIcons.infoVideo} 
                  />
              : safeItem.markerType === 'infoDoc'
                ? <IconGuides 
                    // id={id} 
                    item={item} 
                    icon={settings.markerList.markerTypeIcons.infoDoc} 
                  />
              : safeItem.markerType === 'infoImage'
                ? <IconGuides 
                    // id={id} 
                    item={item} 
                    icon={settings.markerList.markerTypeIcons.infoImage} 
                  />
              : null
          }
        </div>
      </Html>
    </MarkerPosition>
  )
}

export default function _360InfoMarkers({ markerList, set_360Object }) {
  // Force re-render when markerList changes
  const [, forceUpdate] = useState()

  // Ensure markerList is an array
  const safeMarkerList = Array.isArray(markerList) ? markerList : []

  useEffect(() => {
    // Force a re-render when markerList changes
    forceUpdate({})
  }, [safeMarkerList])

  // console.log('_360InfoMarkers:',markerList)

  return (
    <>
      {/* Markers section */}
      {safeMarkerList.map((item, index) => (
        // Use a unique key based on name and index to ensure proper updates
        <MarkerIcon
          key={`marker-${item?.name || 'unnamed'}-${index}`}
          item={item}
          set_360Object={set_360Object}
        />
      ))}
    </>
  )
}
