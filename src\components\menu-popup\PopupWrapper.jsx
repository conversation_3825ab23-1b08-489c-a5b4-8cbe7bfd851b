'use client'
import { settings } from '@/lib/settings'
import React, { useRef, useState } from 'react'
import Image from 'next/image'
import { useContextExperience } from '@/contexts/useContextExperience'
import { useRouter, useSearchParams } from 'next/navigation'
import MenuPopup from './MenuWrapper'
import { ACTIONS_EXPERIENCE_STATE } from '@/contexts/reducerExperience'
import BookingFormComponent from '../BookingFormComponent'
import VideoGalleryComponent from './VideoGalleryComponent'
import GalleryStoreComponent from './GalleryStoreComponent'
import ItemInfoComponent from './ItemInfoComponent'

export default function PopupWrapper() {
    const lineClass2='w-full border-1 border-gray-400/30'
    const lineClass='w-full border-1 mt-2 border-gray-400/30'
    const refGroup=useRef()
    const {experienceState,disptachExperience}=useContextExperience()
    const router=useRouter()
    const searchParams=useSearchParams()
    const query=searchParams.get('id')
    // console.log('PopupWrapper:',query)

    // console.log('PopupWrapper:',experienceState)

  return (
    (experienceState?.showPopup && <div className='popupWrapper flex z-10 absolute top-0 left-0 w-full h-full bg-black/75'>
      <div 
        onClick={e=>disptachExperience({type:ACTIONS_EXPERIENCE_STATE.POPUP_BOOKING_TOGGLE})} 
        className=" flex z-10 items-center justify-center absolute top-0 right-[104px] h-[75px] w-[96px] cursor-pointer"
      >
        <div className={`rotate-45  bg-[#e7e0da] h-1 m-auto absolute w-10 duration-300 ease-linear`}/>
        <div className={`-rotate-45 bg-[#e7e0da] h-1 m-auto absolute w-10 duration-300 ease-linear`}/>
      </div>
      <div className='flex relative top-[75px] left-[75px] w-[calc(100%-75px)] h-[calc(100%-75px)] overflow-y-auto overflow-x-hidden'>
        {experienceState?.showBookingPopup && <BookingFormComponent/>}
        {experienceState?.showGalleryStore && <GalleryStoreComponent/>}
        {experienceState?.showVideoGallery && <VideoGalleryComponent/>}
        {experienceState?.showItemInfo && <ItemInfoComponent/>}
      </div>
    </div>)
  )
}
