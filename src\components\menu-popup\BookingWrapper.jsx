'use client'
import { useContextExperience } from '@/contexts/useContextExperience'
import { ACTIONS_EXPERIENCE_STATE } from '@/contexts/reducerExperience'
import BookingFormComponent from '../BookingFormComponent'

export default function BookingWrapper() {
    const lineClass2='w-full border-1 border-gray-400/30'
    const lineClass='w-full border-1 mt-2 border-gray-400/30'
    const {experienceState,disptachExperience}=useContextExperience()
    // console.log('PopupWrapper:',query)

    const handleBooking=()=>{
      console.log('booking close')
        disptachExperience({type:ACTIONS_EXPERIENCE_STATE.POPUP_BOOKING_TOGGLE})
    }

    // console.log('BookingWrapper:',experienceState)

  return (
    (experienceState?.showBookingPopup && <div className='popup-wrapper flex z-10 absolute top-0 left-0 w-full h-full bg-black/75'>
      <div 
        onClick={handleBooking} 
        className=" flex z-40 items-center justify-center absolute top-0 right-[104px] h-[75px] w-[96px] cursor-pointer"
      >
        <div className={`rotate-45  bg-[#e7e0da] h-1 m-auto absolute w-10 duration-300 ease-linear`}/>
        <div className={`-rotate-45 bg-[#e7e0da] h-1 m-auto absolute w-10 duration-300 ease-linear`}/>
      </div>
      <div className='flex relative top-[75px] left-[75px] w-[calc(100%-75px)] h-[calc(100%-75px)] overflow-y-auto overflow-x-hidden'>
        <BookingFormComponent/>
      </div>
    </div>)
  )
}
