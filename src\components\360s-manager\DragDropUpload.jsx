'use client';

import { useState, useCallback, useRef } from 'react';
import { MdCloudUpload, MdDelete, MdImage, MdError, MdCheckCircle, MdHourglass, MdHourglassTop } from 'react-icons/md';

export default function DragDropUpload({ 
  onFilesSelected, 
  maxFiles = 10, 
  maxSize = 20 * 1024 * 1024, // 20MB
  acceptedTypes = ['image/jpeg', 'image/png', 'image/tiff'],
  disabled = false 
}) {
  const [isDragOver, setIsDragOver] = useState(false);
  const [files, setFiles] = useState([]);
  const [errors, setErrors] = useState([]);
  const fileInputRef = useRef(null);

  // Validate file function
  const validateFile = useCallback((file) => {
    const errors = [];
    
    // Check file type
    if (!acceptedTypes.includes(file.type)) {
      errors.push(`Invalid file type. Accepted: ${acceptedTypes.join(', ')}`);
    }
    
    // Check file size
    if (file.size > maxSize) {
      const sizeMB = (maxSize / (1024 * 1024)).toFixed(1);
      errors.push(`File size exceeds ${sizeMB}MB limit`);
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }, [acceptedTypes, maxSize]);

  // Process files function
  const processFiles = useCallback((fileList) => {
    const newFiles = [];
    const newErrors = [];

    Array.from(fileList).forEach((file, index) => {
      // Check max files limit
      if (files.length + newFiles.length >= maxFiles) {
        newErrors.push({
          file: file.name,
          errors: [`Maximum ${maxFiles} files allowed`]
        });
        return;
      }

      // Check for duplicates
      const isDuplicate = files.some(f => f.file.name === file.name && f.file.size === file.size) ||
                         newFiles.some(f => f.file.name === file.name && f.file.size === file.size);
      
      if (isDuplicate) {
        newErrors.push({
          file: file.name,
          errors: ['File already selected']
        });
        return;
      }

      // Validate file
      const validation = validateFile(file);
      
      if (validation.isValid) {
        // Create preview
        const reader = new FileReader();
        const fileData = {
          id: `${file.name}-${file.size}-${Date.now()}`,
          file,
          name: file.name.replace(/\.[^/.]+$/, ''), // Auto-extract name without extension
          preview: null,
          status: 'ready'
        };

        reader.onload = (e) => {
          fileData.preview = e.target.result;
          setFiles(prev => prev.map(f => f.id === fileData.id ? fileData : f));
        };
        reader.readAsDataURL(file);

        newFiles.push(fileData);
      } else {
        newErrors.push({
          file: file.name,
          errors: validation.errors
        });
      }
    });

    // Update state
    setFiles(prev => [...prev, ...newFiles]);
    setErrors(prev => [...prev, ...newErrors]);

    // Notify parent component
    if (newFiles.length > 0) {
      onFilesSelected([...files, ...newFiles]);
    }

    // Clear errors after 5 seconds
    if (newErrors.length > 0) {
      setTimeout(() => {
        setErrors(prev => prev.filter(error => !newErrors.includes(error)));
      }, 5000);
    }
  }, [files, maxFiles, validateFile, onFilesSelected]);

  // Drag and drop handlers
  const handleDragEnter = useCallback((e) => {
    e.preventDefault();
    e.stopPropagation();
    if (!disabled) {
      setIsDragOver(true);
    }
  }, [disabled]);

  const handleDragLeave = useCallback((e) => {
    e.preventDefault();
    e.stopPropagation();
    if (!disabled) {
      setIsDragOver(false);
    }
  }, [disabled]);

  const handleDragOver = useCallback((e) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  const handleDrop = useCallback((e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);

    if (disabled) return;

    const droppedFiles = e.dataTransfer.files;
    if (droppedFiles.length > 0) {
      processFiles(droppedFiles);
    }
  }, [disabled, processFiles]);

  // File input handler
  const handleFileInput = useCallback((e) => {
    if (disabled) return;
    
    const selectedFiles = e.target.files;
    if (selectedFiles.length > 0) {
      processFiles(selectedFiles);
    }
    
    // Reset input value to allow selecting the same file again
    e.target.value = '';
  }, [disabled, processFiles]);

  // Remove file function
  const removeFile = useCallback((fileId) => {
    setFiles(prev => {
      const updatedFiles = prev.filter(f => f.id !== fileId);
      onFilesSelected(updatedFiles);
      return updatedFiles;
    });
  }, [onFilesSelected]);

  // Clear all files
  const clearAllFiles = useCallback(() => {
    setFiles([]);
    setErrors([]);
    onFilesSelected([]);
  }, [onFilesSelected]);

  return (
    <div className="space-y-4">
      {/* Drag and Drop Zone */}
      <div
        className={`relative border-2 border-dashed rounded-lg p-8 text-center transition-all duration-200 ${
          isDragOver
            ? 'border-blue-500 bg-blue-50'
            : disabled
            ? 'border-gray-200 bg-gray-50 cursor-not-allowed'
            : 'border-gray-300 hover:border-gray-400 cursor-pointer'
        }`}
        onDragEnter={handleDragEnter}
        onDragLeave={handleDragLeave}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
        onClick={() => !disabled && fileInputRef.current?.click()}
      >
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept={acceptedTypes.join(',')}
          onChange={handleFileInput}
          className="hidden"
          disabled={disabled}
        />

        <div className="space-y-4">
          <MdHourglassTop 
            className={`mx-auto text-6xl ${
              isDragOver ? 'text-blue-500' : disabled ? 'text-gray-400' : 'text-gray-500'
            }`} 
          />
          
          <div>
            <p className={`text-lg font-medium ${disabled ? 'text-gray-400' : 'text-gray-700'}`}>
              {isDragOver ? 'Drop files here' : 'Drag & drop 360° images here'}
            </p>
            <p className={`text-sm ${disabled ? 'text-gray-400' : 'text-gray-500'}`}>
              or click to browse files
            </p>
          </div>

          <div className={`text-xs ${disabled ? 'text-gray-400' : 'text-gray-500'}`}>
            <p>Supported formats: JPEG, PNG, TIFF</p>
            <p>Maximum file size: {(maxSize / (1024 * 1024)).toFixed(1)}MB</p>
            <p>Maximum files: {maxFiles}</p>
          </div>
        </div>
      </div>

      {/* Error Messages */}
      {errors.length > 0 && (
        <div className="space-y-2">
          {errors.map((error, index) => (
            <div key={index} className="bg-red-50 border border-red-200 rounded-md p-3">
              <div className="flex items-start space-x-2">
                <MdError className="text-red-500 mt-0.5 flex-shrink-0" />
                <div className="text-sm">
                  <p className="font-medium text-red-800">{error.file}</p>
                  <ul className="text-red-600 mt-1">
                    {error.errors.map((err, errIndex) => (
                      <li key={errIndex}>• {err}</li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* File Previews */}
      {files.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900">
              Selected Files ({files.length})
            </h3>
            <button
              onClick={clearAllFiles}
              className="text-sm text-red-600 hover:text-red-800 transition-colors"
            >
              Clear All
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {files.map((fileData) => (
              <div key={fileData.id} className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm">
                {/* Preview Image */}
                <div className="relative h-32 bg-gray-100">
                  {fileData.preview ? (
                    <img
                      src={fileData.preview}
                      alt={fileData.name}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center">
                      <MdImage className="text-4xl text-gray-400" />
                    </div>
                  )}
                  
                  {/* Delete Button */}
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      removeFile(fileData.id);
                    }}
                    className="absolute top-2 right-2 p-1 bg-red-600 text-white rounded-full hover:bg-red-700 transition-colors"
                  >
                    <MdDelete className="w-4 h-4" />
                  </button>

                  {/* Status Indicator */}
                  <div className="absolute bottom-2 left-2">
                    {fileData.status === 'ready' && (
                      <MdCheckCircle className="text-green-500 w-5 h-5" />
                    )}
                    {fileData.status === 'uploading' && (
                      <MdHourglassTop className="text-blue-500 w-5 h-5 animate-pulse" />
                    )}
                    {fileData.status === 'completed' && (
                      <MdCheckCircle className="text-green-600 w-5 h-5" />
                    )}
                    {fileData.status === 'error' && (
                      <MdError className="text-red-500 w-5 h-5" />
                    )}
                  </div>
                </div>

                {/* File Info */}
                <div className="p-3">
                  <p className="font-medium text-gray-900 text-sm truncate" title={fileData.name}>
                    {fileData.name}
                  </p>
                  <div className="flex items-center justify-between mt-1">
                    <p className="text-xs text-gray-500">
                      {(fileData.file.size / (1024 * 1024)).toFixed(2)} MB
                    </p>
                    <span className={`text-xs px-2 py-1 rounded-full ${
                      fileData.status === 'ready' ? 'bg-green-100 text-green-800' :
                      fileData.status === 'uploading' ? 'bg-blue-100 text-blue-800' :
                      fileData.status === 'completed' ? 'bg-green-100 text-green-800' :
                      fileData.status === 'error' ? 'bg-red-100 text-red-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {fileData.status === 'ready' ? 'Ready' :
                       fileData.status === 'uploading' ? 'Uploading...' :
                       fileData.status === 'completed' ? 'Completed' :
                       fileData.status === 'error' ? 'Error' :
                       'Unknown'
                      }
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
